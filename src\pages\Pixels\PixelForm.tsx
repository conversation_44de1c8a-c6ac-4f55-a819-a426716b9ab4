import React, { useState, useEffect } from "react";
import { useNavigate, Link, useParams } from "react-router-dom";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { pixelService, vcardService } from "../../services/api";
import { Breadcrumb } from "react-bootstrap";
import { FiChevronRight } from "react-icons/fi";
import { VCard } from "../../services/vcard";

interface Pixel {
  id: number;
  name: string;
  vcard_id: number;
  is_active: boolean;
  vcard?: VCard;
}

const PixelForm: React.FC = () => {
  const { id } = useParams<{ id?: string }>();
  const isEditMode = Boolean(id);

  const [formData, setFormData] = useState({
    name: "",
    vcardId: "",
    is_active: true,
  });

  const [vcards, setVcards] = useState<VCard[]>([]);
  const [userId, setUserId] = useState<number | null>(null);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [pixelDetails, setPixelDetails] = useState<Pixel | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const loadUserAndData = async () => {
      const userData = localStorage.getItem("user");
      if (!userData) {
        toast.error("User not authenticated");
        navigate("/login");
        return;
      }

      try {
        const user = JSON.parse(userData);
        setUserId(user.id);

        const vcardsResponse = await vcardService.getAll(user.id.toString());
        setVcards(vcardsResponse);

        if (!isEditMode && (!vcardsResponse || vcardsResponse.length === 0)) {
          toast.warning(
            "You need to create at least one vCard before creating a pixel."
          );
          navigate("/admin/vcard/create");
          return;
        }

        if (isEditMode && id) {
          const response = await pixelService.getPixelById(id);
          
          const pixel = response.data || response;
          
          if (!pixel) {
            toast.error("Pixel not found");
            navigate("/admin/pixel");
            return;
          }

          setPixelDetails(pixel);
          setFormData({
            name: pixel.name,
            vcardId: pixel.vcard_id?.toString() || "",
            is_active: pixel.is_active,
          });
        }
      } catch (error) {
        console.error("Error loading data:", error);
        toast.error(
          isEditMode ? "Failed to load pixel" : "Failed to load user data"
        );
        navigate("/admin/pixel");
      }
    };

    loadUserAndData();
  }, [id, isEditMode, navigate]);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "Name must be at least 2 characters long";
    } else if (formData.name.trim().length > 50) {
      newErrors.name = "Name must be less than 50 characters";
    }

    if (!formData.vcardId) {
      newErrors.vcardId = "vCard selection is required";
    }

    if (!userId) {
      newErrors.user = "User not authenticated";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
      return;
    }

    setIsSubmitting(true);

    try {
      const payload = {
        name: formData.name.trim(),
        vcardId: parseInt(formData.vcardId),
        is_active: formData.is_active,
        userId: userId!,
      };

      if (isEditMode && id) {
        await pixelService.update(id, payload);
        toast.success("Pixel updated successfully!");
      } else {
        await pixelService.create(payload);
        toast.success("Pixel created successfully!");
      }
      
      window.scrollTo({ top: 0, behavior: 'smooth' });
      setTimeout(() => navigate("/admin/pixel"), 2000);
    } catch (error: any) {
      console.error("Submission error:", error);
      const errorMessage =
        error.response?.data?.error ||
        error.response?.data?.message ||
        error.message ||
        "Operation failed";
      toast.error(`Error: ${errorMessage}`);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const breadcrumbLinks = [
    { name: "Pixels", path: "/admin/pixel" },
    { name: isEditMode ? "Edit Pixel" : "Create Pixel", path: location.pathname },
  ];

  const renderStatusBadge = (isActive: boolean) => {
    return (
      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
        isActive 
          ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300" 
          : "bg-gray-100 dark:bg-gray-700/50 text-gray-800 dark:text-gray-300"
      }`}>
        {isActive ? "Active" : "Inactive"}
      </span>
    );
  };

  return (
    <div className="p-4 sm:p-6 lg:px-8 xl:px-28 w-full max-w-[90rem] mx-auto">
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
      />

      <Breadcrumb className="mb-4 sm:mb-6">
        {breadcrumbLinks.map((link, index) => (
          <Breadcrumb.Item
            key={index}
            linkAs={Link}
            linkProps={{ to: link.path }}
            active={index === breadcrumbLinks.length - 1}
            className={`text-sm font-medium ${
              index === breadcrumbLinks.length - 1
                ? "text-primary"
                : "text-gray-600 hover:text-primary"
            }`}
          >
            {index < breadcrumbLinks.length - 1 ? (
              <div className="flex items-center">
                {link.name}
                <FiChevronRight className="mx-2 text-gray-400" size={14} />
              </div>
            ) : (
              link.name
            )}
          </Breadcrumb.Item>
        ))}
      </Breadcrumb>

      <div className="w-full flex flex-col bg-gray-50 dark:bg-gray-900 mx-auto">
        <div className="flex flex-col items-center justify-center px-4">
          <div className="w-full max-w-2xl">
            <div className="text-center mb-8 w-full">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                {isEditMode ? "Edit Pixel" : "Create New Pixel"}
              </h3>
              <p className="text-primary">
                {isEditMode
                  ? "Update your pixel tracking settings"
                  : "Configure a new tracking pixel for analytics"}
              </p>
            </div>

            <form
              className="space-y-6"
              onSubmit={handleSubmit}
              autoComplete="off"
            >
              <div className="space-y-2">
                <label
                  htmlFor="name"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  Pixel Name <span className="text-red-500">*</span>
                </label>
                <div className="inputForm-vcard bg-gray-100 dark:bg-gray-800 rounded-lg">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg
                      className="h-5 w-5 text-gray-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                  </div>
                  <input
                    id="name"
                    type="text"
                    className={`input-vcard ${errors.name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                    placeholder="Enter pixel name"
                    value={formData.name}
                    onChange={(e) => {
                      setFormData((prev) => ({ ...prev, name: e.target.value }));
                      if (errors.name) {
                        setErrors((prev) => ({ ...prev, name: "" }));
                      }
                    }}
                    autoComplete="off"
                    autoSave="off"
                    autoCorrect="off"
                    spellCheck="false"
                    required
                    aria-describedby={errors.name ? "name-error" : "name-help"}
                    aria-invalid={!!errors.name}
                  />
                </div>
                {errors.name && (
                  <small id="name-error" className="text-red-500 text-sm flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {errors.name}
                  </small>
                )}
                <p id="name-help" className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Enter a descriptive name for your tracking pixel
                </p>
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="vcardId"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  Associated vCard <span className="text-red-500">*</span>
                </label>
                <div className="inputForm-vcard bg-gray-100 dark:bg-gray-800 rounded-lg">
                  <select
                    id="vcardId"
                    value={formData.vcardId}
                    onChange={(e) => {
                      setFormData((prev) => ({
                        ...prev,
                        vcardId: e.target.value,
                      }));
                      if (errors.vcardId) {
                        setErrors((prev) => ({ ...prev, vcardId: "" }));
                      }
                    }}
                    className={`input-vcard w-full bg-transparent dark:bg-gray-800 dark:text-gray-300
                              border-gray-300 dark:border-gray-600 rounded-lg focus:border-transparent
                              dark:[color-scheme:dark] focus:ring-2 focus:ring-purple-500 ${
                                errors.vcardId ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                              }`}
                    autoComplete="off"
                    required
                    aria-describedby={errors.vcardId ? "vcardId-error" : "vcardId-help"}
                    aria-invalid={!!errors.vcardId}
                  >
                    <option value="" className="dark:bg-gray-800 dark:text-gray-300">
                      Select a vCard
                    </option>
                    {vcards.map((vcard) => (
                      <option
                        key={vcard.id}
                        value={vcard.id.toString()}
                        className="dark:bg-gray-800 dark:text-gray-300"
                      >
                        {vcard.name}
                      </option>
                    ))}
                  </select>
                </div>
                {errors.vcardId && (
                  <small id="vcardId-error" className="text-red-500 text-sm flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {errors.vcardId}
                  </small>
                )}
                <p id="vcardId-help" className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Select the vCard to associate with this tracking pixel
                </p>
              </div>

              {isEditMode && pixelDetails && (
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Current Status
                  </label>
                  <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        {renderStatusBadge(pixelDetails.is_active)}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Tracking Status
                </label>
                <div className="flex items-center gap-4 bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                  <label className="flex items-center cursor-pointer">
                    <div className="relative">
                      <input
                        type="checkbox"
                        className="sr-only"
                        checked={formData.is_active}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            is_active: e.target.checked,
                          }))
                        }
                      />
                      <div
                        className={`w-12 h-6 rounded-full shadow-inner transition-colors duration-200
                        ${
                          formData.is_active
                            ? "bg-purple-500"
                            : "bg-gray-300 dark:bg-gray-600"
                        }`}
                      >
                        <div
                          className={`absolute top-1 w-4 h-4 bg-white rounded-full shadow-md transform transition-transform duration-200
                          ${
                            formData.is_active
                              ? "translate-x-6"
                              : "translate-x-1"
                          }`}
                        />
                      </div>
                    </div>
                    <span className="ml-3 text-gray-700 dark:text-gray-300">
                      {formData.is_active
                        ? "Active Tracking"
                        : "Paused Tracking"}
                    </span>
                  </label>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Enable or disable tracking for this pixel
                </p>
              </div>

              {/* Information Box */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <h4 className="font-medium text-blue-800 dark:text-blue-200 flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  Tracking Pixel Information
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300 mt-2">
                  This pixel will track visits and interactions with your selected vCard. 
                  Analytics data will be available in your dashboard once the pixel is active.
                </p>
              </div>

              {/* Submit Button */}
              <div className="pt-6">
                <div className="flex flex-col sm:flex-row gap-3">
                  <button
                    type="button"
                    onClick={() => navigate("/admin/pixel")}
                    className="w-full sm:w-auto flex justify-center py-3 px-6 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-base font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors"
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="w-full sm:flex-1 flex justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={isSubmitting || !formData.name.trim() || !formData.vcardId}
                  >
                    {isSubmitting ? (
                      <span className="flex items-center">
                        <svg
                          className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          aria-hidden="true"
                        >
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {isEditMode ? "Saving..." : "Creating..."}
                      </span>
                    ) : (
                      isEditMode ? "Save Changes" : "Create Pixel"
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PixelForm;