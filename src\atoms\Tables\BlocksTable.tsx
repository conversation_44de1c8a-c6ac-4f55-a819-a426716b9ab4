import React from 'react';
import { FaTrash, FaToggleOn, FaToggleOff } from 'react-icons/fa';
import { Block } from '../../services/vcard';
import { getBlockIcon } from '../../services/blockIcons';

interface BlocksTableProps {
  blocks: Block[];
  onDelete: (blockId: string) => void;
  onToggleStatus: (blockId: string, isActive: boolean) => void;
}

const renderStatusBadge = (isActive: boolean) => {
  return isActive ? (
    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
      Active
    </span>
  ) : (
    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
      Inactive
    </span>
  );
};

const BlockRow: React.FC<{ 
  block: Block; 
  onDelete: (blockId: string) => void;
  onToggleStatus: (blockId: string, isActive: boolean) => void;
}> = ({ block, onDelete, onToggleStatus }) => {
  const { icon: Icon, gradient, shadow } = getBlockIcon(block.type_block);

  return (
    <tr className="hover:bg-gray-50 dark:hover:bg-gray-700">
      <td className="px-4 py-3 whitespace-nowrap">
        <div className="flex justify-center">
          <div className={`p-3 rounded-full bg-gradient-to-br ${gradient} ${shadow} flex items-center justify-center`}>
            <Icon className="w-5 h-5 text-white" />
          </div>
        </div>
      </td>
      
      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
        {block.type_block}
      </td>
      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
        {block.name}
      </td>
      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
        {block.description || 'N/A'}
      </td>
      <td className="px-4 py-3 whitespace-nowrap">
        {renderStatusBadge(block.status || false)}
      </td>
      <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
        <div className="flex justify-end space-x-2">
          <button
            onClick={() => onToggleStatus(block.id, !block.status)}
            className={`p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 ${
              block.status 
                ? 'text-yellow-600 hover:text-yellow-900 dark:text-yellow-400' 
                : 'text-green-600 hover:text-green-900 dark:text-green-400'
            }`}
            title={block.status ? "Deactivate" : "Activate"}
          >
            {block.status ? <FaToggleOff /> : <FaToggleOn />}
          </button>
          <button
            onClick={() => onDelete(block.id)}
            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"
            title="Delete"
          >
            <FaTrash />
          </button>
        </div>
      </td>
    </tr>
  );
};

const BlocksTable: React.FC<BlocksTableProps> = ({
  blocks,
  onDelete,
  onToggleStatus
}) => {
  return (
    <div className="overflow-x-auto rounded-lg shadow w-full max-w-full">
      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-gray-800">
          <tr>
            <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Icon
            </th>
            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Type
            </th>
            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Name
            </th>
            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Description
            </th>
            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Status
            </th>
            <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          {blocks.length > 0 ? (
            blocks.map((block) => (
              <BlockRow
                key={block.id}
                block={block}
                onDelete={onDelete}
                onToggleStatus={onToggleStatus}
              />
            ))
          ) : (
            <tr>
              <td colSpan={6} className="px-6 py-12 text-center">
                <div className="text-center py-4">
                  <div className="text-gray-400 text-3xl mb-2">📦</div>
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                    No blocks found
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400 max-w-xs mx-auto text-xs">
                    Create your first block to get started
                  </p>
                </div>
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};

export default BlocksTable;