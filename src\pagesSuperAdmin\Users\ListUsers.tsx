import React, { useState, useEffect } from 'react';
import {
  FaSearch,
  FaTrash,
  FaEdit,
  FaUser<PERSON>lash,
  <PERSON>a<PERSON>ser<PERSON><PERSON><PERSON>,
  FaCrown,
  Fa<PERSON>ser,
  FaUserTie,
  FaFilter,
  FaTimes,
  FaAngleLeft,
  FaAngleRight,
  FaPlus,
  FaFileExport,
  FaFileCsv,
  FaFileCode
} from 'react-icons/fa';
import { Link } from 'react-router-dom';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { authService } from '../../services/api';
import LoadingSpinner from '../../Loading/LoadingSpinner';
import EmptyState from '../../cards/EmptyState';
import { User } from '../../services/user';

interface ActiveFilters {
  status: string;
  role: string;
  verified: string;
  search: string;
}

const ListUsers: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [exporting, setExporting] = useState(false);
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [activeFilters, setActiveFilters] = useState<ActiveFilters>({
    status: 'all',
    role: 'all',
    verified: 'all',
    search: ''
  });
  
  const itemsPerPage = 20;

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const response = await authService.getAllUsers({
          page: currentPage,
          limit: itemsPerPage
        });
        
        if (response.data) {
          setUsers(response.data.data);
          setFilteredUsers(response.data.data);
        }
      } catch (error) {
        console.error('Failed to fetch users', error);
        toast.error('Failed to load users. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [currentPage]);

  // Appliquer les filtres
  useEffect(() => {
    let filtered = [...users];
    
    if (activeFilters.search) {
      filtered = filtered.filter(user => 
        user.name.toLowerCase().includes(activeFilters.search.toLowerCase()) ||
        user.email.toLowerCase().includes(activeFilters.search.toLowerCase())
      );
    }
    
    if (activeFilters.status !== 'all') {
      filtered = filtered.filter(user => 
        activeFilters.status === 'active' ? user.isActive : !user.isActive
      );
    }
    
    if (activeFilters.role !== 'all') {
      filtered = filtered.filter(user => user.role === activeFilters.role);
    }
    
    if (activeFilters.verified !== 'all') {
      filtered = filtered.filter(user => 
        activeFilters.verified === 'verified' ? user.isVerified : !user.isVerified
      );
    }
    
    setFilteredUsers(filtered);
  }, [activeFilters, users]);

  // Gestion des filtres
  const handleFilterChange = (filterType: keyof ActiveFilters, value: string) => {
    setActiveFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const resetFilters = () => {
    setActiveFilters({
      status: 'all',
      role: 'all',
      verified: 'all',
      search: ''
    });
  };

  const hasActiveFilters = () => {
    return (
      activeFilters.status !== 'all' ||
      activeFilters.role !== 'all' ||
      activeFilters.verified !== 'all' ||
      activeFilters.search !== ''
    );
  };

  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  // Actions sur les utilisateurs
  const toggleUserStatus = async (userId: string, isActive: boolean) => {
    try {
      const response = await authService.toggleUserStatus(Number(userId), isActive);
      if (response.success) {
        toast.success(`User ${isActive ? 'activated' : 'deactivated'} successfully`);
        setUsers(users.map(user =>
          user.id === userId ? { ...user, isActive } : user
        ));
      }
    } catch (error) {
      console.error('Failed to toggle user status', error);
      toast.error('Failed to update user status');
    }
  };

  const changeUserRole = async (userId: string, role: 'user' | 'admin') => {
    try {
      toast.warning('Change user role functionality is not yet implemented');
      console.log(`Would change user ${userId} role to ${role}`);

      setUsers(users.map(user =>
        user.id === userId ? { ...user, role } : user
      ));
    } catch (error) {
      console.error('Failed to change user role', error);
      toast.error('Failed to update user role');
    }
  };

  const deleteUser = async (userId: string) => {
    if (window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      try {
        toast.warning('Delete user functionality is not yet implemented');
        console.log(`Would delete user ${userId}`);

        setUsers(users.filter(user => user.id !== userId));
      } catch (error) {
        console.error('Failed to delete user', error);
        toast.error('Failed to delete user');
      }
    }
  };

  const handleExport = (format: 'csv' | 'json') => {
    if (exporting) return;
    
    try {
      setExporting(true);
      setShowExportMenu(false);
      
      const dataToExport = filteredUsers.map(user => ({
        ID: user.id,
        Name: user.name,
        Email: user.email,
        Role: user.role,
        Status: user.isActive ? 'Active' : 'Inactive',
        Verified: user.isVerified ? 'Yes' : 'No',
        'Created At': new Date(user.createdAt).toLocaleDateString()
      }));
      
      if (format === 'csv') {
        // Implémentation de l'export CSV
        toast.success('CSV export initiated');
      } else {
        // Implémentation de l'export JSON
        toast.success('JSON export initiated');
      }
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Export failed');
    } finally {
      setExporting(false);
    }
  };

  // Rendu des badges de rôle
  const renderRoleBadge = (role: string) => {
    switch (role) {
      case 'superAdmin':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
            <FaCrown className="mr-1" /> Super Admin
          </span>
        );
      case 'admin':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            <FaUserTie className="mr-1" /> Admin
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
            <FaUser className="mr-1" /> User
          </span>
        );
    }
  };

  // Rendu des badges d'état
  const renderStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
        Active
      </span>
    ) : (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
        Inactive
      </span>
    );
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="p-4 sm:p-6 lg:px-8 xl:px-28 w-full max-w-[90rem] mx-auto">
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
      />

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 sm:mb-8 gap-4">
        <div className="w-full md:w-auto">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">User Management</h1>
          <p className="text-primary mt-1 sm:mt-2 text-sm sm:text-base">
            View and manage all system users
          </p>
        </div>

        <div className="w-full md:w-auto flex flex-wrap items-center gap-3">
          <div className="relative flex-1 min-w-[200px]">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaSearch className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400 dark:text-gray-500" />
            </div>
            <input
              type="text"
              placeholder="Search users..."
              className="w-full pl-9 sm:pl-10 pr-4 py-2 sm:py-2.5 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all text-sm sm:text-base"
              value={activeFilters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>

          <div className="flex items-center gap-2 sm:gap-4 self-end sm:self-auto">
            <div className="relative">
              <button
                className={`p-2 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-center h-10 w-10 sm:h-12 sm:w-12 border ${
                  hasActiveFilters()
                    ? 'border-red-500'
                    : 'border-purple-500'
                } hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200`}
                onClick={() => setShowFilterMenu(!showFilterMenu)}
              >
                <FaFilter className={
                  hasActiveFilters()
                    ? 'text-red-500'
                    : 'text-purple-500'
                } />
              </button>

              {/*{showFilterMenu && (
                <FilterCard
                  activeFilters={activeFilters}
                  onFilterChange={handleFilterChange}
                  onResetFilters={resetFilters}
                  onClose={() => setShowFilterMenu(false)}
                />
              )}*/}
            </div>

            <div className="relative">
              <button
                className="p-2 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-center h-10 w-10 sm:h-12 sm:w-12 border border-purple-500 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
                aria-label="Export options"
                onClick={() => setShowExportMenu(!showExportMenu)}
                disabled={exporting || filteredUsers.length === 0}
              >
                <FaFileExport className={`text-purple-500 text-sm sm:text-base ${exporting ? 'opacity-50' : ''}`} />
              </button>

              {showExportMenu && (
                <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-10">
                  <div className="py-1">
                    <button
                      className="w-full px-4 py-2 text-sm text-left text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                      onClick={() => handleExport('csv')}
                      disabled={exporting}
                    >
                      <FaFileCsv className="text-green-500" />
                      <span>Export as CSV</span>
                    </button>
                    <button
                      className="w-full px-4 py-2 text-sm text-left text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                      onClick={() => handleExport('json')}
                      disabled={exporting}
                    >
                      <FaFileCode className="text-blue-500" />
                      <span>Export as JSON</span>
                    </button>
                  </div>
                </div>
              )}
            </div>

            <button
              onClick={() => {}}
              className="flex items-center justify-center bg-purple-500 hover:bg-purple-600 text-white font-medium py-2 px-4 sm:py-2.5 sm:px-6 rounded-lg transition-colors h-10 sm:h-12 text-sm sm:text-base"
            >
              <FaPlus className="mr-2" />
              <span>Add User</span>
            </button>
          </div>
        </div>
      </div>

      {hasActiveFilters() && (
        <div className="mb-4 flex items-center gap-2 flex-wrap">
          <span className="text-sm text-gray-500 dark:text-gray-400">Active filters:</span>
          {activeFilters.status !== 'all' && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              Status: {activeFilters.status === 'active' ? 'Active' : 'Inactive'}
            </span>
          )}
          {activeFilters.role !== 'all' && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              Role: {activeFilters.role}
            </span>
          )}
          {activeFilters.verified !== 'all' && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
              Verified: {activeFilters.verified === 'verified' ? 'Yes' : 'No'}
            </span>
          )}
          {activeFilters.search && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
              Search: "{activeFilters.search}"
            </span>
          )}
          <button
            onClick={resetFilters}
            className="text-xs text-red-500 hover:text-red-700 dark:hover:text-red-400 flex items-center"
          >
            <FaTimes className="mr-1" /> Clear all
          </button>
        </div>
      )}

      <div className="overflow-x-auto rounded-lg shadow">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                User
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Role
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Verified
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Created
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {filteredUsers.length > 0 ? (
              filteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-750">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        {user.avatar ? (
                          <img className="h-10 w-10 rounded-full" src={user.avatar} alt={user.name} />
                        ) : (
                          <div className="bg-gray-200 border-2 border-dashed rounded-xl w-10 h-10 flex items-center justify-center text-gray-400">
                            <FaUser />
                          </div>
                        )}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {user.name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {user.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {renderRoleBadge(user.role)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {renderStatusBadge(user.isActive)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {user.isVerified ? (
                      <span className="text-green-600 dark:text-green-400">Yes</span>
                    ) : (
                      <span className="text-yellow-600 dark:text-yellow-400">No</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {new Date(user.createdAt).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      {user.role !== 'superAdmin' && (
                        <>
                          <button
                            onClick={() => toggleUserStatus(user.id, !user.isActive)}
                            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
                            title={user.isActive ? "Deactivate user" : "Activate user"}
                          >
                            {user.isActive ? (
                              <FaUserSlash className="text-yellow-500" />
                            ) : (
                              <FaUserCheck className="text-green-500" />
                            )}
                          </button>
                          
                          {user.role !== 'admin' && (
                            <button
                              onClick={() => changeUserRole(user.id, 'admin')}
                              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
                              title="Make Admin"
                            >
                              <FaUserTie className="text-blue-500" />
                            </button>
                          )}
                          
                          {user.role === 'admin' && (
                            <button
                              onClick={() => changeUserRole(user.id, 'user')}
                              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
                              title="Remove Admin"
                            >
                              <FaUser className="text-gray-500" />
                            </button>
                          )}
                          
                          <button
                            onClick={() => deleteUser(user.id)}
                            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
                            title="Delete user"
                          >
                            <FaTrash className="text-red-500" />
                          </button>
                        </>
                      )}
                      
                      <Link
                        to={`/admin/users/${user.id}/edit`}
                        className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
                        title="Edit user"
                      >
                        <FaEdit className="text-purple-500" />
                      </Link>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className="px-6 py-12 text-center">
                  <EmptyState
                    title={hasActiveFilters() ? "No users match your filters" : "No users found"}
                    description={hasActiveFilters() 
                      ? "Try adjusting your search or filters" 
                      : "Create your first user to get started"}
                    actionText="Add User"
                    actionLink="#"
                    icon={<FaUser className="text-4xl mx-auto text-gray-400 mb-4" />}
                  />
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {filteredUsers.length > 0 && totalPages > 1 && (
        <div className="flex justify-end mt-8">
          <nav className="flex items-center gap-1">
            <button
              onClick={() => paginate(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className={`p-2 rounded-md flex items-center justify-center ${
                currentPage === 1
                  ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600'
              }`}
            >
              <FaAngleLeft className="h-4 w-4" />
            </button>

            <div className="flex items-center gap-1 mx-2">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((number) => (
                <button
                  key={number}
                  onClick={() => paginate(number)}
                  className={`w-8 h-8 text-sm rounded-md flex items-center justify-center ${
                    currentPage === number
                      ? 'bg-purple-500 text-white font-medium'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600'
                  }`}
                >
                  {number}
                </button>
              ))}
            </div>

            <button
              onClick={() => paginate(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className={`p-2 rounded-md flex items-center justify-center ${
                currentPage === totalPages
                  ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600'
              }`}
            >
              <FaAngleRight className="h-4 w-4" />
            </button>
          </nav>
        </div>
      )}
    </div>
  );
};

export default ListUsers;