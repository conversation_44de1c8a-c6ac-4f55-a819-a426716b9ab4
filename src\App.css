@tailwind base;
@tailwind components;
@tailwind utilities;

.signup-container {
  display: flex;
  height: 100vh;
}
.form-signup {
  display: flex;
  flex-direction: column;
  gap: 5px;
  background-color: #ffffff;
  border-radius: 20px;
  align-items: center;
  margin-top: 1rem;
}
.form-container-signup {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f7f7f7;
}

.form-wrapper-signup {
  background-color: white;
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
}

.image-container-signup {
  flex: 1;
  background-color: #e0e0e0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-container-signup .image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.flex-column-signup {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 5px;
}

.inputForm-signup {
  border: 1.5px solid #ecedec;
  border-radius: 10px;
  height: 35px;
  display: flex;
  align-items: center;
  padding-left: 10px;
  transition: 0.2s ease-in-out;
}

.input-signup {
  margin-left: 10px;
  border-radius: 10px;
  border: none;
  width: 85%;
  height: 95%;
}

.input-signup:focus {
  outline: none;
}

.inputForm-signup:focus-within {
  border: 1.5px solid #06A3DA;
}

.button-submit-signup {
  margin: 0 0 5px 0;
  border: none;
  color: white;
  background-color: #06A3DA;
  font-size: 15px;
  font-weight: 500;
  border-radius: 10px;
  height: 50px;
  width: 100%;
  cursor: pointer;
}

.button-submit-reset-pwd {
  margin: 15px 0 15px 0;
  border: none;
  color: white;
  font-size: 15px;
  font-weight: 500;
  border-radius: 10px;
  height: 50px;
  width: 100%;
  cursor: pointer;
}
.btn-signup {
  width: 100%;
  height: 50px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  gap: 10px;
  border: 1px solid #ededef;
  background-color: white;
  cursor: pointer;
  transition: 0.2s ease-in-out;
}

.btn-signup:hover {
  border: 1px solid #2d79f3;
}
.span-signup {
  font-size: 14px;
  margin-left: 5px;
  color: #c60b4a;
  font-weight: 500;
  cursor: pointer;
}

.p-signup {
  text-align: center;
  font-size: 14px;
}

.custom-checkbox {
  display: flex;
  align-items: center;
  margin: 1.5rem 0;
}

.custom-checkbox input[type="checkbox"] {
  opacity: 0;
  position: absolute;
}

.custom-checkbox label {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.custom-checkbox label::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  border: 2px solid #c60b4a;
  border-radius: 4px;
  background-color: white;
  transition: background-color 0.3s, border-color 0.3s;
}

.custom-checkbox label::after {
  content: "";
  position: absolute;
  left: 6px;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
  opacity: 0;
  transition: opacity 0.3s;
}

.custom-checkbox input[type="checkbox"]:checked + label::before {
  background-color: #c60b4a;
  border-color: #c60b4a;
}

.custom-checkbox input[type="checkbox"]:checked + label::after {
  opacity: 1;
}

.custom-checkbox a {
  color: #c60b4a;
  text-decoration: none;
}

.custom-checkbox a:hover {
  text-decoration: underline;
}

.text-danger {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

@media (max-width: 768px) {
  .signup-container {
    flex-direction: column; 
  }

  .form-container-signup {
    padding: 10px;
    place-items: center;
  }

  .image-container-signup {
    display: none; 
  }

  .form-wrapper-signup {
    padding: 1.5rem;
  }

  .inputForm-signup {
    height: 45px;
  }

  .button-submit-signup {
    height: 45px;
    font-size: 14px;
  }

  .btn-signup {
    height: 45px;
    font-size: 14px;
  }
}

.forgot-password-link {
  margin-left: auto;
  cursor: pointer;
  color: #c60b4a;
  text-decoration: underline;
}

.forgot-password-link:hover {
  color: #c60b4a;
}

.recaptcha{
  padding-top: 5px;
}

.terms-container {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  max-width: 1000px;
  margin: 50px auto;
  padding: 20px;
}

.terms-header {
  background-color: #f4f4f4;
  padding: 20px;
  text-align: center;
  margin-bottom: 20px;
}

.terms-header h1 {
  font-size: 2.5em;
  color: #c60b4a;
}

.terms-content h2 {
  font-size: 2em;
  margin-top: 10px;
  margin-bottom: 10px;
  color: #2c3e50;
}

.terms-content h3 {
  font-size: 1.5em;
  margin-top: 20px;
  margin-bottom: 10px;
  color: #2c3e50;
}

.terms-content p {
  margin-bottom: 15px;
}
@media (max-width: 768px) {
  .terms-container {
      padding: 10px;
  }

  .terms-header h1 {
      font-size: 2em;
  }

  .terms-content h2 {
      font-size: 1.5em;
  }

  .terms-content h3 {
      font-size: 1.2em;
  }
}

.bg-dark{
  background-color: #000;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out forwards;
}

.padding-image{
  padding-top: 150px;
}

.inputForm-vcard {
  position: relative;
  border: 1.5px solid #bebebe;
  border-radius: 10px;
  display: flex;
  align-items: center;
  transition: 0.2s ease-in-out;
  padding: 0.75rem 1rem;
}

.input-vcard {
  border-radius: 10px;
  border: none;
  width: 100%;
  padding-left: 2rem;
  background: transparent;
  resize: none;
}

.input-vcard:focus {
  outline: none;
}

.inputForm-vcard:focus-within {
  border: 1.5px solid #06A3DA;
  box-shadow: 0 0 0 1px #06A3DA;
}

.dark .inputForm-vcard {
  border-color: #4b5563;
}

.dark .input-vcard {
  color: white;
  background-color: transparent;
}

.dark .inputForm-vcard:focus-within {
  border-color: #06A3DA;
}

.btn-vcard {
  width: 100%; 
  max-width: 500px; 
}

@media (max-width: 768px) {
  .inputForm-vcard {
    height: auto;
    min-height: 35px; 
  }

  .input-vcard {
    height: auto; 
    min-height: 30px; 
  }

  textarea.input-vcard {
    min-height: 100px; 
  }

  .btn-vcard {
    padding: 8px 16px; 
  }
}

@media (max-width: 480px) {
  .inputForm-vcard {
    height: auto; 
    min-height: 30px;
    width: 100%; 
  }

  .input-vcard {
    height: auto;
    min-height: 25px; 
  }

  textarea.input-vcard {
    min-height: 80px; 
  }

  .btn-vcard {
    font-size: 14px; 
  }
}
.container-checkbox input {
  display: none;
}

.container-checkbox {
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  font-size: 16px;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  gap: 8px;
}

.label-text {
  color: #333;
}

.checkmark {
  position: relative;
  top: 0;
  left: 0;
  height: 1.3em;
  width: 1.3em;
  background-color: #2196f300;
  border-radius: 0.25em;
  transition: all 0.25s;
  border: 2px solid #06A3DA;
}

.container-checkbox input:checked ~ .checkmark {
  background-color: #06A3DA;
  border-color: #06A3DA;
}

.checkmark:after {
  content: "";
  position: absolute;
  transform: rotate(0deg);
  border: 0.1em solid black;
  left: 0;
  top: 0;
  width: 1.05em;
  height: 1.05em;
  border-radius: 0.25em;
  transition:
    all 0.25s,
    border-width 0.1s;
  border-color: #06A3DA;
}

.container-checkbox input:checked ~ .checkmark:after {
  left: 0.45em;
  top: 0.25em;
  width: 0.25em;
  height: 0.5em;
  border-color: #fff0 white white #fff0;
  border-width: 0 0.15em 0.15em 0;
  border-radius: 0em;
  transform: rotate(45deg);
}

.edit-vcard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.edit-vcard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.edit-vcard-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-primary);
}

.edit-vcard-url {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary);
  margin-top: 0.5rem;
}

.edit-vcard-url button {
  color: var(--text-secondary);
  transition: color 0.2s;
}

.edit-vcard-url button:hover {
  color: var(--text-primary);
}

.edit-vcard-form {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.edit-vcard-section {
  background: var(--bg-card);
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.edit-vcard-section-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.edit-vcard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.edit-vcard-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.dark .edit-vcard-section {
  background: var(--dark-bg-card);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

@media (max-width: 768px) {
  .edit-vcard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .edit-vcard-actions {
    flex-direction: column;
    gap: 1rem;
  }
  
  .edit-vcard-grid {
    grid-template-columns: 1fr;
  }
}

.bg-settings-container {
  background: var(--bg-card);
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.bg-settings-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.bg-preset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.75rem;
}

.bg-preset-btn {
  position: relative;
  height: 80px;
  border-radius: 0.5rem;
  border: 2px solid var(--border-color);
  transition: all 0.2s ease;
  cursor: pointer;
  overflow: hidden;
}

.bg-preset-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.bg-preset-btn.active {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

.bg-preset-check {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.2);
}

.bg-color-preview {
  width: 3rem;
  height: 3rem;
  border-radius: 0.5rem;
  cursor: pointer;
  border: 1px solid var(--border-color);
}

.bg-gradient-preview {
  width: 100%;
  height: 6rem;
  border-radius: 0.5rem;
  margin: 1rem 0;
  border: 1px solid var(--border-color);
}

.dark .bg-settings-container {
  background: var(--dark-bg-card);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.dark .bg-preset-btn {
  border-color: var(--dark-border-color);
}

.dark .bg-color-preview {
  border-color: var(--dark-border-color);
}

.dark .bg-gradient-preview {
  border-color: var(--dark-border-color);
}

@media (max-width: 640px) {
  .bg-preset-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.inputForm-vcard select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1em;
}

.dark .inputForm-vcard select {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  color: white;
}

@-moz-document url-prefix() {
  .dark select option {
    background-color: #1f2937;
    color: white;
  }
}

.dark select::-webkit-scrollbar {
  width: 8px;
}

.dark select::-webkit-scrollbar-track {
  background: #1f2937;
}

.dark select::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 4px;
}

.dark select::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

.dark select {
  scrollbar-width: thin;
  scrollbar-color: #4b5563 #1f2937;
}

.modal-overlay {
  @apply fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4;
}

.modal-content {
  @apply bg-white rounded-xl shadow-2xl w-full max-w-md overflow-hidden dark:bg-gray-800 transition-all duration-300 transform;
}

.modal-header {
  @apply flex items-center justify-between px-6 py-4 border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-gray-700 dark:to-gray-700;
}

.modal-body {
  @apply px-6 py-5 space-y-5;
}

.modal-footer {
  @apply px-6 py-4 bg-gray-50 dark:bg-gray-700/50 flex justify-end space-x-3 border-t border-gray-100 dark:border-gray-600 rounded-b-xl;
}

.input-field {
  @apply relative;
}

.input-icon {
  @apply absolute inset-y-0 flex items-center pointer-events-none text-gray-400;
}

.input-control {
  @apply w-full pl-10 pr-4 py-2.5 border rounded-lg focus:ring-2 focus:ring-blue-50 focus:border-blue-50 transition-all bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500;
}

.input-error {
  @apply border-red-500;
}

.input-normal {
  @apply border-gray-200 dark:border-gray-600;
}

.react-tel-input .form-control {
  @apply w-full pl-10 pr-4 py-2.5 border rounded-lg focus:ring-2 focus:ring-blue-50 focus:border-blue-50 transition-all h-auto bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
}

.react-tel-input .flag-dropdown {
  @apply bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 rounded-l-lg;
}

.react-tel-input .selected-flag {
  @apply px-3;
}

.react-tel-input .country-list {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50;
}

.react-tel-input .country:hover, .react-tel-input .country.highlight {
  @apply bg-gray-100 dark:bg-gray-700;
}


.react-tel-input .search-box {
  @apply bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-600;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.phone-dropdown .country:hover,
.phone-dropdown .country.highlight {
  background-color: #06A3DA !important; 
  color: white !important; 
}

.phone-dropdown {
  background: white !important; 
  color: #374151 !important; 
}

.dark .phone-dropdown {
  background: #1f2937 !important; 
  color: white !important; 
}

.breadcrumb {
  @apply flex items-center space-x-2;
}

.breadcrumb-item {
  @apply flex items-center;
}

.breadcrumb-item.active {
  @apply text-primary font-semibold;
}

.block-card {
  @apply transition-all duration-300 transform hover:-translate-y-1 hover:shadow-md;
}

.search-input {
  @apply transition-all duration-200;
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.dark .breadcrumb-item:not(.active) {
  @apply text-gray-400;
}

.dark .search-input {
  @apply bg-gray-800 border-gray-700 text-white;
}

@media (max-width: 640px) {
  .breadcrumb {
    @apply text-xs;
  }
  
  .block-card {
    @apply min-h-[120px];
  }
}

.block-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-gradient {
  background: linear-gradient(135deg, #06A3DA 0%, #0589B7 100%);
}

.type-badge {
  background-color: rgba(6, 163, 218, 0.1);
  transition: background-color 0.2s;
}

.dark .type-badge {
  background-color: rgba(6, 163, 218, 0.2);
}

.block-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.card-actions {
  transition: opacity 0.2s ease-in-out;
}

@media (max-width: 640px) {
  .block-card {
    padding: 12px;
  }
  
  .card-content {
    padding-right: 8px;
  }
}
.dropdown-menu {
  max-height: 200px;
  overflow-y: auto;
}
.dropdown-menu-top {
  bottom: 100%;
  top: auto;
}

.datepicker-wrapper {
  width: 100%;
}

.datepicker-popper {
  z-index: 1000;
}

.react-datepicker {
  font-size: 0.8rem !important;
  width: 100% !important;
}
.react-datepicker__month-container {
  width: 100% !important;
}
.react-datepicker__header {
  padding: 0.5rem !important;
  background-color: white !important;
}
.react-datepicker__month {
  margin: 0.3rem !important;
}
.react-datepicker__day-name, 
.react-datepicker__day {
  width: 1.8rem !important;
  line-height: 1.8rem !important;
  margin: 0.15rem !important;
}
.react-datepicker__current-month {
  font-size: 0.9rem !important;
  padding-bottom: 0.5rem !important;
}
.react-datepicker__navigation {
  top: 0.5rem !important;
}
.react-datepicker__day--selected {
  background-color: #8b5cf6 !important;
}
.dark .react-datepicker {
  background-color: #1f2937 !important;
  border-color: #374151 !important;
}
.dark .react-datepicker__header {
  background-color: #1f2937 !important;
  border-color: #374151 !important;
}
.dark .react-datepicker__current-month {
  color: #f3f4f6 !important;
}
.dark .react-datepicker__day-name {
  color: #9ca3af !important;
}
.dark .react-datepicker__day {
  color: #f3f4f6 !important;
}
.dark .react-datepicker__day--outside-month {
  color: #6b7280 !important;
}

.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker__input-container {
  width: 100%;
}

@media (max-width: 640px) {
  .react-datepicker {
    font-size: 0.9rem;
  }
  
  .react-datepicker__day {
    width: 2rem;
    line-height: 2rem;
  }
}

@media (max-width: 640px) {
  .responsive-calendar {
    width: 100% !important;
    font-size: 14px !important;
  }
  
  .responsive-calendar .react-datepicker__month-container {
    width: 100% !important;
  }
  
  .responsive-calendar .react-datepicker__day {
    width: 2rem !important;
    height: 2rem !important;
    line-height: 2rem !important;
  }
  
  .react-datepicker__input-container input {
    width: 100% !important;
  }
}

@media (min-width: 641px) {
  .responsive-calendar {
    width: 280px !important;
  }
}

.react-datepicker-popper {
  z-index: 60 !important;
}

.dark .responsive-calendar {
  background-color: #1f2937 !important;
  border-color: #374151 !important;
  color: #f3f4f6 !important;
}

.dark .responsive-calendar .react-datepicker__header {
  background-color: #1f2937 !important;
  border-color: #374151 !important;
}

.dark .responsive-calendar .react-datepicker__current-month,
.dark .responsive-calendar .react-datepicker__day-name,
.dark .responsive-calendar .react-datepicker__day {
  color: #f3f4f6 !important;
}

.dark .responsive-calendar .react-datepicker__day--outside-month {
  color: #6b7280 !important;
}

.dark .responsive-calendar .react-datepicker__day--selected {
  background-color: #8b5cf6 !important;
  color: white !important;
}

.react-select-container {
  width: 100%;
  font-size: 0.875rem;
}

.react-select__control {
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  min-height: 42px !important;
  background-color: white !important;
  transition: all 0.2s;
}

.react-select__control--is-focused {
  box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.5) !important;
  outline: none !important;
}

.react-select__value-container {
  padding: 0.25rem 0.75rem !important;
}

.react-select__input-container {
  margin: 0 !important;
  padding: 0 !important;
}

.react-select__menu {
  margin-top: 0.25rem !important;
  border-radius: 0.375rem !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.react-select__option {
  padding: 0.5rem 0.75rem !important;
}

.react-select__option--is-selected {
  background-color: #e9d5ff !important;
  color: #000 !important;
}

.react-select__option--is-focused {
  background-color: #f3e8ff !important;
}

.react-select__indicator {
  padding: 0.5rem !important;
  color: #9ca3af !important;
}

.react-select__indicator:hover {
  color: #6b7280 !important;
}

.dark .react-select__control {
  background-color: #1a1c23 !important;
}

.dark .react-select__single-value {
  color: #f3f4f6 !important;
}

.dark .react-select__input {
  color: #f3f4f6 !important;
}

.dark .react-select__menu {
  background-color: #1a1c23 !important;
}

.dark .react-select__option {
  background-color: #1a1c23 !important;
  color: #f3f4f6 !important;
}

.dark .react-select__option--is-selected {
  background-color: #6b21a8 !important;
  color: #f3f4f6 !important;
}

.dark .react-select__option--is-focused {
  background-color: #7e22ce !important;
}

.table-logs {
  width: 100%;
  margin: 0 auto;
}

@media (min-width: 1025px) {
  .table-logs {
    max-width: 100%;
    overflow-x: visible;
  }
}

@media (max-width: 1024px) {
  .table-logs {
    max-width: 65vw;
    overflow-x: auto;
  }
}

@media (max-width: 768px) {
  .table-logs {
    max-width: 75vw;
    overflow-x: auto;
  }
  
  .table-logs table {
    min-width: 700px; 
  }
}

@media (max-width: 480px) {
  .table-logs {
    max-width: 70vw;
    overflow-x: auto;
  }
  
  .table-logs table {
    min-width: 600px; 
  }
  
  .table-logs th,
  .table-logs td {
    padding: 8px 4px; 
  }
}

@media (max-width: 320px) {
  .table-logs {
    max-width: 65vw;
    padding: 0;
    margin: 0;
  }
  
  .table-logs th,
  .table-logs td {
    padding: 6px 3px; 
    font-size: 0.75rem; 
  }
}

@media (max-width: 400px) {
  .block-card {
    max-width: 85vw;
    padding: 0;
    margin: 0;
  }
  .div-block{
    padding: 0;
    margin-left: -50px;
  }
  .name {
    visibility: hidden;
  }
}

@media (max-width: 767px) {
  .block-card {
    max-width: 80vw;
    padding: 10px;
    margin: 10px;
  }
  .div-block{
    margin-left: -60px;
    margin-right: -60px;
    padding-left: 0;
    padding-right:0;
  }
}

