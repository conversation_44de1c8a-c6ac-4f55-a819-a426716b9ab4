import React, { useState, useEffect } from 'react';
import { FaPlus } from 'react-icons/fa';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import PlanCardAdmin from '../../cards/PlanCardAdmin';
import { planService } from '../../services/api';
import { Plan } from '../../services/Plan';
import LoadingSpinner from '../../Loading/LoadingSpinner';
import StatsCards from '../../cards/StatsCard';

const ListPlans: React.FC = () => {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddPlanModal, setShowAddPlanModal] = useState(false);

  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    free: 0,
    default: 0
  });

  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setLoading(true);
        const response = await planService.getAllPlans();
        
        if (response.data) {
          setPlans(response.data);
        } else {
          setPlans([]);
          toast.error('No plan data received from server');
        }
      } catch (error) {
        console.error('Failed to fetch plans', error);
        toast.error('Failed to load plans. Please try again.');
        setPlans([]);
      } finally {
        setLoading(false);
      }
    };

    fetchPlans();
  }, []);

  useEffect(() => {
    if (plans.length > 0) {
      const total = plans.length;
      const active = plans.filter(plan => plan.is_active).length;
      const free = plans.filter(plan => Number(plan.price) === 0).length;
      const defaultPlans = plans.filter(plan => plan.is_default).length;

      setStats({ total, active, free, default: defaultPlans });
    } else {
      setStats({ total: 0, active: 0, free: 0, default: 0 });
    }
  }, [plans]);

  const handleCreatePlan = async (newPlan: Plan) => {
    try {
      const response = await planService.createPlan(newPlan);
      
      if (response.data) {
        setPlans(prev => [response.data as Plan, ...prev]);
        toast.success('Plan created successfully!');
        setShowAddPlanModal(false);
        
        setStats(prev => ({
          ...prev,
          total: prev.total + 1,
          active: prev.active + (newPlan.is_active ? 1 : 0),
          free: prev.free + (Number(newPlan.price) === 0 ? 1 : 0),
          default: prev.default + (newPlan.is_default ? 1 : 0)
        }));
      }
    } catch (error: any) {
      console.error('Failed to create plan:', error);
      toast.error(error.response?.data?.message || 'Failed to create plan');
    }
  };

  const togglePlanStatus = async (planId: number, isActive: boolean) => {
    try {
      // Optimistic UI update
      setPlans(prevPlans =>
        prevPlans.map(plan =>
          plan.id === planId ? { ...plan, is_active: isActive } : plan
        )
      );

      await planService.togglePlanStatus(planId.toString());
      toast.success(`Plan ${isActive ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      console.error('Failed to toggle plan status', error);
      toast.error('Failed to update plan status');

      // Revert UI update on error
      setPlans(prevPlans =>
        prevPlans.map(plan =>
          plan.id === planId ? { ...plan, is_active: !isActive } : plan
        )
      );
    }
  };

  const handleDeletePlan = async (planId: number) => {
    if (window.confirm('Are you sure you want to delete this plan? This action cannot be undone.')) {
      try {
        await planService.deletePlan(planId.toString());
        
        setPlans(prev => prev.filter(plan => plan.id !== planId));
        toast.success('Plan deleted successfully');
        
        // Update stats
        const deletedPlan = plans.find(p => p.id === planId);
        if (deletedPlan) {
          setStats(prev => ({
            ...prev,
            total: prev.total - 1,
            active: prev.active - (deletedPlan.is_active ? 1 : 0),
            free: prev.free - (Number(deletedPlan.price) === 0 ? 1 : 0),
            default: prev.default - (deletedPlan.is_default ? 1 : 0)
          }));
        }
      } catch (error) {
        console.error('Failed to delete plan', error);
        toast.error('Failed to delete plan');
      }
    }
  };

  const setDefaultPlan = async (planId: number) => {
    try {
      // First, remove default status from all plans
      const updatedPlans = plans.map(plan => ({
        ...plan,
        is_default: plan.id === planId
      }));
      
      setPlans(updatedPlans);
      
      // Then make API call to set the new default
      await planService.updatePlan(planId.toString(), { is_default: true });
      
      // Also update any other plan that was default to false
      const previousDefault = plans.find(p => p.is_default && p.id !== planId);
      if (previousDefault) {
        await planService.updatePlan(previousDefault.id.toString(), { is_default: false });
      }
      
      toast.success('Default plan updated successfully');
    } catch (error) {
      console.error('Failed to set default plan', error);
      toast.error('Failed to update default plan');
      setPlans(plans);
    }
  };

  const handleEditPlan = async (plan: Plan) => {
    // Implement your edit modal logic here
    console.log('Edit plan:', plan);
    toast.info('Edit plan functionality to be implemented');
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="p-4 sm:p-6 lg:px-8 xl:px-28 w-full max-w-[90rem] mx-auto">
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
      />

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 sm:mb-8 gap-4">
        <div className="w-full md:w-auto">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">Plan Management</h1>
          <p className="text-primary mt-1 sm:mt-2 text-sm sm:text-base">
            View and manage all subscription plans
          </p>
        </div>

        <div className="w-full md:w-auto flex flex-wrap items-center gap-3">
            <button
              onClick={() => setShowAddPlanModal(true)}
              className="flex items-center justify-center bg-purple-500 hover:bg-purple-600 text-white font-medium py-2 px-4 sm:py-2.5 sm:px-6 rounded-lg transition-colors h-10 sm:h-12 text-sm sm:text-base relative"
            >
              <FaPlus className="absolute left-1/2 transform -translate-x-1/2 sm:static sm:transform-none sm:mr-2 w-10" />
              <span className="hidden xs:inline sm:ml-0">Add Plan</span>
            </button>
        </div>
      </div>

      <StatsCards
        stats={{
          total: stats.total,
          active: stats.active,
          verified: stats.free,
          admins: stats.default,
          superAdmins: 0
        }}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {plans.map((plan: Plan) => (
          <PlanCardAdmin
            key={plan.id}
            plan={plan}
            isCurrent={false}
            onEdit={() => handleEditPlan(plan)}
            onDelete={() => handleDeletePlan(plan.id!)}
            onToggleStatus={() => togglePlanStatus(plan.id!, !plan.is_active)}
            onSetDefault={() => setDefaultPlan(plan.id!)}
          />
        ))}
      </div>

      {/* Ajoutez votre AddPlanModal ici */}
    </div>
  );
};

export default ListPlans;