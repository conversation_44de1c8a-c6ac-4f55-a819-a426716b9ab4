import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>aS<PERSON>ch, Fa<PERSON>ilter, FaPlus, FaFileExport } from 'react-icons/fa';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import PlanCard from '../../cards/PlanCard';
import { planService } from '../../services/api';
import { Plan } from '../../services/Plan';
import LoadingSpinner from '../../Loading/LoadingSpinner';
import StatsCards from '../../cards/StatsCard';
import ExportMenu from '../../cards/ExportMenu';
import ActiveFilters from '../../cards/ActiveFilters';
import Pagination from '../../atoms/Pagination/Pagination';
//import AddPlanModal from '../../modals/AddPlanModal';

export interface ActiveFilters {
  status: string;
  type: string;
  search: string;
}

const ListPlans: React.FC = () => {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [exporting, setExporting] = useState(false);
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [showAddPlanModal, setShowAddPlanModal] = useState(false);
  
  
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    free: 0,
    default: 0
  });
  
  const exportMenuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setLoading(true);
        const response = await planService.getAllPlans();
        
        if (response.data) {
          setPlans(response.data);
        } else {
          setPlans([]);
          toast.error('No plan data received from server');
        }
      } catch (error) {
        console.error('Failed to fetch plans', error);
        toast.error('Failed to load plans. Please try again.');
        setPlans([]);
      } finally {
        setLoading(false);
      }
    };

    fetchPlans();
  }, []);

  useEffect(() => {
    if (plans.length > 0) {
      const total = plans.length;
      const active = plans.filter(plan => plan.is_active).length;
      const free = plans.filter(plan => Number(plan.price) === 0).length;
      const defaultPlans = plans.filter(plan => plan.is_default).length;
      
      setStats({ total, active, free, default: defaultPlans });
    } else {
      setStats({ total: 0, active: 0, free: 0, default: 0 });
    }
  }, [plans]);


  const formatPlanData = (plan: Plan) => ({
    ID: plan.id,
    Name: plan.name,
    Description: plan.description,
    Price: `$${plan.price}`,
    Duration: `${plan.duration_days} days`,
    Features: Array.isArray(plan.features) ? plan.features.join(', ') : '',
    Active: plan.is_active ? 'Yes' : 'No',
    Default: plan.is_default ? 'Yes' : 'No',
    'Created At': plan.created_at ? new Date(plan.created_at).toLocaleString() : 'N/A'
  });

  /*const handleExport = (format: 'csv' | 'json') => {
    if (exporting || !filteredPlans || filteredPlans.length === 0) return;
    
    try {
      setExporting(true);
      setShowExportMenu(false);
      
      const date = new Date().toISOString().slice(0, 10);
      const filename = `plans_export_${date}`;
      
      if (format === 'csv') {
        exportToCsv(filteredPlans.map(formatPlanData), filename);
      } else {
        exportToJson(filteredPlans.map(formatPlanData), filename);
      }
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Export failed');
    } finally {
      setExporting(false);
    }
  };*/

  const exportToCsv = (data: any[], filename: string) => {
    if (!data || data.length === 0) return;
    
    const csvContent = [
      Object.keys(data[0]).join(','),
      ...data.map(row => 
        Object.values(row).map(value => 
          typeof value === 'string' && value.includes(',') 
            ? `"${value.replace(/"/g, '""')}"` 
            : value
        ).join(',')
      )
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast.success('CSV export completed successfully');
  };

  const exportToJson = (data: any[], filename: string) => {
    if (!data || data.length === 0) return;
    
    const jsonString = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}.json`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast.success('JSON export completed successfully');
  };

  const handleCreatePlan = async (newPlan: Plan) => {
    try {
      const response = await planService.createPlan(newPlan);
      
      if (response.data) {
        setPlans(prev => [response.data, ...prev]);
        toast.success('Plan created successfully!');
        setShowAddPlanModal(false);
        
        setStats(prev => ({
          ...prev,
          total: prev.total + 1,
          active: prev.active + (newPlan.is_active ? 1 : 0),
          free: prev.free + (Number(newPlan.price) === 0 ? 1 : 0),
          default: prev.default + (newPlan.is_default ? 1 : 0)
        }));
      }
    } catch (error: any) {
      console.error('Failed to create plan:', error);
      toast.error(error.response?.data?.message || 'Failed to create plan');
    }
  };

  const togglePlanStatus = async (planId: number, isActive: boolean) => {
    try {
      // Optimistic UI update
      setPlans(prevPlans =>
        prevPlans.map(plan =>
          plan.id === planId ? { ...plan, is_active: isActive } : plan
        )
      );

      await planService.togglePlanStatus(planId.toString());
      toast.success(`Plan ${isActive ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      console.error('Failed to toggle plan status', error);
      toast.error('Failed to update plan status');

      // Revert UI update on error
      setPlans(prevPlans =>
        prevPlans.map(plan =>
          plan.id === planId ? { ...plan, is_active: !isActive } : plan
        )
      );
    }
  };

  const handleDeletePlan = async (planId: number) => {
    if (window.confirm('Are you sure you want to delete this plan?')) {
      try {
        await planService.deletePlan(planId.toString());
        
        setPlans(prev => prev.filter(plan => plan.id !== planId));
        toast.success('Plan deleted successfully');
        
        // Update stats
        const deletedPlan = plans.find(p => p.id === planId);
        if (deletedPlan) {
          setStats(prev => ({
            ...prev,
            total: prev.total - 1,
            active: prev.active - (deletedPlan.is_active ? 1 : 0),
            free: prev.free - (Number(deletedPlan.price) === 0 ? 1 : 0),
            default: prev.default - (deletedPlan.is_default ? 1 : 0)
          }));
        }
      } catch (error) {
        console.error('Failed to delete plan', error);
        toast.error('Failed to delete plan');
      }
    }
  };

  const setDefaultPlan = async (planId: number) => {
    try {
      // First, remove default status from all plans
      const updatedPlans = plans.map(plan => ({
        ...plan,
        is_default: plan.id === planId
      }));
      
      setPlans(updatedPlans);
      
      // Then make API call to set the new default
      await planService.updatePlan(planId.toString(), { is_default: true });
      
      // Also update any other plan that was default to false
      const previousDefault = plans.find(p => p.is_default && p.id !== planId);
      if (previousDefault) {
        await planService.updatePlan(previousDefault.id.toString(), { is_default: false });
      }
      
      toast.success('Default plan updated successfully');
    } catch (error) {
      console.error('Failed to set default plan', error);
      toast.error('Failed to update default plan');
      // Revert UI update on error
      setPlans(plans);
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="p-4 sm:p-6 lg:px-8 xl:px-28 w-full max-w-[90rem] mx-auto">
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
      />

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 sm:mb-8 gap-4">
        <div className="w-full md:w-auto">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">Plan Management</h1>
          <p className="text-primary mt-1 sm:mt-2 text-sm sm:text-base">
            View and manage all subscription plans
          </p>
        </div>

        <div className="w-full md:w-auto flex flex-wrap items-center gap-3">
          <div className="relative flex-1 min-w-[200px]">
            <button
              onClick={() => setShowAddPlanModal(true)}
              className="flex items-center justify-center bg-purple-500 hover:bg-purple-600 text-white font-medium py-2 px-4 sm:py-2.5 sm:px-6 rounded-lg transition-colors h-10 sm:h-12 text-sm sm:text-base"
            >
              <FaPlus className="mr-2" />
              <span>Add Plan</span>
            </button>
          </div>
        </div>
      </div>

      <StatsCards 
        stats={[
          { label: 'Total Plans', value: stats.total },
          { label: 'Active Plans', value: stats.active },
          { label: 'Free Plans', value: stats.free },
          { label: 'Default Plan', value: stats.default }
        ]} 
      />
      
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {currentPagePlans.map(plan => (
              <div key={plan.id} className="relative">
                <PlanCard 
                  plan={plan}
                  isCurrent={false}
                  onSelect={() => {}}
                />
                
                <div className="mt-3 flex justify-between">
                  <button
                    onClick={() => togglePlanStatus(plan.id!, !plan.is_active)}
                    className={`px-3 py-1 rounded text-sm ${
                      plan.is_active 
                        ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                        : 'bg-green-100 text-green-700 hover:bg-green-200'
                    }`}
                  >
                    {plan.is_active ? 'Deactivate' : 'Activate'}
                  </button>
                  
                  <button
                    onClick={() => plan.is_default ? {} : setDefaultPlan(plan.id!)}
                    className={`px-3 py-1 rounded text-sm ${
                      plan.is_default 
                        ? 'bg-blue-100 text-blue-700 cursor-default' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                    disabled={plan.is_default}
                  >
                    {plan.is_default ? 'Default' : 'Set Default'}
                  </button>
                  
                  <button
                    onClick={() => handleDeletePlan(plan.id!)}
                    className="px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200"
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>

        </>
      )}

      <AddPlanModal 
        isOpen={showAddPlanModal}
        onClose={() => setShowAddPlanModal(false)}
        onCreatePlan={handleCreatePlan}
      />
    </div>
  );
};

export default ListPlans;