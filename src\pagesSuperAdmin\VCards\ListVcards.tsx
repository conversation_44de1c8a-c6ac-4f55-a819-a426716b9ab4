import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Fa<PERSON><PERSON>ch, FaFilter, FaFileExport, FaEye, FaTrash } from 'react-icons/fa';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { vcardService } from '../../services/api';
import LoadingSpinner from '../../Loading/LoadingSpinner';
import { VCardWithUser } from '../../services/api';
import Pagination from '../../atoms/Pagination/Pagination';
import ActiveFilters from '../../cards/ActiveFilters';
import { formatDate } from '../../services/dateUtils';
import { Link } from 'react-router-dom';
import ActiveFiltersVcards from '../../cards/ActiveFiltersVcards';

export interface ActiveFilters {
  status: string;
  user: string;
  search: string;
}

const ListVCards: React.FC = () => {
  const [allVCards, setAllVCards] = useState<VCardWithUser[]>([]); 
  const [loading, setLoading] = useState(true);
  const [exporting, setExporting] = useState(false);
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [activeFilters, setActiveFilters] = useState<ActiveFilters>({
    status: 'all',
    user: 'all',
    search: ''
  });
  
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    users: 0
  });
  
  const itemsPerPage = 10;
  const exportMenuRef = useRef<HTMLDivElement>(null);
  const filterButtonRef = useRef<HTMLButtonElement>(null);
  const filterMenuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (allVCards.length > 0) {
      const total = allVCards.length;
      const active = allVCards.filter(vcard => vcard.is_active).length;
      const inactive = allVCards.filter(vcard => !vcard.is_active).length;
      
      // Compter le nombre d'utilisateurs uniques
      const uniqueUserIds = new Set(allVCards.map(vcard => vcard.Users?.id));
      
      setStats({ 
        total, 
        active, 
        inactive,
        users: uniqueUserIds.size 
      });
    } else {
      setStats({ total: 0, active: 0, inactive: 0, users: 0 });
    }
  }, [allVCards]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showExportMenu && exportMenuRef.current && 
          !exportMenuRef.current.contains(event.target as Node)) {
        setShowExportMenu(false);
      }

      if (showFilterMenu && 
          filterMenuRef.current && 
          !filterMenuRef.current.contains(event.target as Node) &&
          filterButtonRef.current && 
          !filterButtonRef.current.contains(event.target as Node)) {
        setShowFilterMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showFilterMenu, showExportMenu]);

  useEffect(() => {
    const fetchVCards = async () => {
      try {
        setLoading(true);
        const response = await vcardService.getAllWithUsers();
        
        if (response.data) {
          setAllVCards(response.data);
        } else {
          setAllVCards([]);
          toast.error('No VCard data received from server');
        }
      } catch (error) {
        console.error('Failed to fetch VCards', error);
        toast.error('Failed to load VCards. Please try again.');
        setAllVCards([]);
      } finally {
        setLoading(false);
      }
    };

    fetchVCards();
  }, []);

  const filteredVCards = useMemo(() => {
    if (!allVCards || allVCards.length === 0) return [];

    let result = [...allVCards];
    
    // Filtre par recherche
    if (activeFilters.search) {
      const searchTerm = activeFilters.search.toLowerCase();
      result = result.filter(vcard => 
        (vcard.name?.toLowerCase().includes(searchTerm)) ||
        (vcard.url?.toLowerCase().includes(searchTerm)) ||
        (vcard.Users?.name?.toLowerCase().includes(searchTerm)) ||
        (vcard.Users?.email?.toLowerCase().includes(searchTerm))
      );
    }
    
    // Filtre par statut
    if (activeFilters.status !== 'all') {
      result = result.filter(vcard => 
        activeFilters.status === 'active' ? vcard.is_active : !vcard.is_active
      );
    }
    
    // Filtre par utilisateur
    if (activeFilters.user !== 'all') {
      result = result.filter(vcard => 
        vcard.Users?.id === activeFilters.user
      );
    }
    
    // Trier par date de création (plus récent en premier)
    result.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime() //erreur : createdAt is possibly undefined
    );
    
    return result;
  }, [activeFilters, allVCards]);

  const currentPageVCards = useMemo(() => {
    if (!filteredVCards || filteredVCards.length === 0) return [];
    
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredVCards.slice(startIndex, endIndex);
  }, [filteredVCards, currentPage, itemsPerPage]);

  const handleFilterChange = (filterType: keyof ActiveFilters, value: string) => {
    setActiveFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
    setCurrentPage(1);
  };

  const resetFilters = () => {
    setActiveFilters({
      status: 'all',
      user: 'all',
      search: ''
    });
    setCurrentPage(1);
  };

  const hasActiveFilters = () => {
    return (
      activeFilters.status !== 'all' ||
      activeFilters.user !== 'all' ||
      activeFilters.search !== ''
    );
  };

  const totalPages = Math.ceil((filteredVCards?.length || 0) / itemsPerPage);
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  const formatVCardData = (vcard: VCardWithUser) => ({
    ID: vcard.id,
    Name: vcard.name,
    URL: vcard.url,
    Status: vcard.is_active ? 'Active' : 'Inactive',
    'Created At': vcard.createdAt ? formatDate(vcard.createdAt) : 'N/A',
    'Last Updated': vcard.updatedAt ? formatDate(vcard.updatedAt) : 'N/A',
    'User Name': vcard.Users?.name || 'N/A',
    'User Email': vcard.Users?.email || 'N/A',
    Views: vcard.views || 0
  });

  const handleExport = (format: 'csv' | 'json') => {
    if (exporting || !filteredVCards || filteredVCards.length === 0) return;
    
    try {
      setExporting(true);
      setShowExportMenu(false);
      
      const date = new Date().toISOString().slice(0, 10);
      const filename = `vcards_export_${date}`;
      
      if (format === 'csv') {
        exportToCsv(filteredVCards.map(formatVCardData), filename);
      } else {
        exportToJson(filteredVCards.map(formatVCardData), filename);
      }
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Export failed');
    } finally {
      setExporting(false);
    }
  };

  const exportToCsv = (data: any[], filename: string) => {
    if (!data || data.length === 0) return;
    
    const csvContent = [
      Object.keys(data[0]).join(','),
      ...data.map(row => 
        Object.values(row).map(value => 
          typeof value === 'string' && value.includes(',') 
            ? `"${value.replace(/"/g, '""')}"` 
            : value
        ).join(',')
      )
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast.success('CSV export completed successfully');
  };

  const exportToJson = (data: any[], filename: string) => {
    if (!data || data.length === 0) return;
    
    const jsonString = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}.json`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast.success('JSON export completed successfully');
  };

  const toggleVCardStatus = async (vcardId: string, isActive: boolean) => {
    try {
      setAllVCards(prevVCards =>
        prevVCards.map(vcard =>
          vcard.id === vcardId ? { ...vcard, is_active: isActive } : vcard
        )
      );

      // Ici vous appelleriez l'API pour mettre à jour le statut
      // await vcardService.updateStatus(vcardId, isActive);
      toast.success(`VCard ${isActive ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      console.error('Failed to toggle VCard status', error);
      toast.error('Failed to update VCard status');

      // Revert changes on error
      setAllVCards(prevVCards =>
        prevVCards.map(vcard =>
          vcard.id === vcardId ? { ...vcard, is_active: !isActive } : vcard
        )
      );
    }
  };

  const handleDeleteVCard = async (vcardId: string) => {
    if (window.confirm('Are you sure you want to delete this VCard?')) {
      try {
        // await vcardService.delete(vcardId);
        setAllVCards(prev => prev.filter(vcard => vcard.id !== vcardId));
        toast.success('VCard deleted successfully');
      } catch (error) {
        console.error('Failed to delete VCard', error);
        toast.error('Failed to delete VCard');
      }
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  // Liste des utilisateurs uniques pour le filtre
  const uniqueUsers = Array.from(
    new Map(
      allVCards
        .filter(vcard => vcard.Users)
        .map(vcard => [vcard.Users?.id, vcard.Users])
    ).values()
  );

  return (
    <div className="p-4 sm:p-6 lg:px-8 xl:px-28 w-full max-w-[90rem] mx-auto">
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
      />

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 sm:mb-8 gap-4">
        <div className="w-full md:w-auto">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">VCard Management</h1>
          <p className="text-primary mt-1 sm:mt-2 text-sm sm:text-base">
            View and manage all VCards in the system
          </p>
        </div>

        <div className="w-full md:w-auto flex flex-wrap items-center gap-3">
          <div className="relative flex-1 min-w-[200px]">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaSearch className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400 dark:text-gray-500" />
            </div>
            <input
              type="text"
              placeholder="Search VCards..."
              className="w-full pl-9 sm:pl-10 pr-4 py-2 sm:py-2.5 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all text-sm sm:text-base"
              value={activeFilters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>

          <div className="flex items-center gap-2 sm:gap-4 self-end sm:self-auto">
            <div className="relative">
              <button
                ref={filterButtonRef}
                className={`p-2 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-center h-10 w-10 sm:h-12 sm:w-12 border ${
                  hasActiveFilters()
                    ? 'border-red-500'
                    : 'border-purple-500'
                } hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200`}
                onClick={() => setShowFilterMenu(!showFilterMenu)}
              >
                <FaFilter className={
                  hasActiveFilters()
                    ? 'text-red-500'
                    : 'text-purple-500'
                } />
              </button>

              {showFilterMenu && (
                <div 
                  ref={filterMenuRef}
                  className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-10"
                >
                  <div className="p-4">
                    <h3 className="font-medium text-gray-800 dark:text-white mb-3">Filters</h3>
                    
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Status
                      </label>
                      <select
                        className="w-full rounded-lg border border-gray-300 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white px-3 py-2"
                        value={activeFilters.status}
                        onChange={(e) => handleFilterChange('status', e.target.value)}
                      >
                        <option value="all">All Statuses</option>
                        <option value="active">Active Only</option>
                        <option value="inactive">Inactive Only</option>
                      </select>
                    </div>
                    
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        User
                      </label>
                      <select
                        className="w-full rounded-lg border border-gray-300 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white px-3 py-2"
                        value={activeFilters.user}
                        onChange={(e) => handleFilterChange('user', e.target.value)}
                      >
                        <option value="all">All Users</option>
                        {uniqueUsers.map(user => (
                          <option key={user.id} value={user.id}>
                            {user.name} ({user.email})
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div className="flex justify-between mt-4">
                      <button
                        className="px-3 py-1.5 text-sm bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600"
                        onClick={resetFilters}
                      >
                        Reset
                      </button>
                      <button
                        className="px-3 py-1.5 text-sm bg-purple-500 text-white rounded hover:bg-purple-600"
                        onClick={() => setShowFilterMenu(false)}
                      >
                        Apply
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="relative" ref={exportMenuRef}>
              <button
                className="p-2 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-center h-10 w-10 sm:h-12 sm:w-12 border border-purple-500 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
                aria-label="Export options"
                onClick={() => setShowExportMenu(!showExportMenu)}
                disabled={exporting || !filteredVCards || filteredVCards.length === 0}
              >
                <FaFileExport className={`text-purple-500 text-sm sm:text-base ${exporting ? 'opacity-50' : ''}`} />
              </button>

              {showExportMenu && (
                <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-10">
                  <div className="py-1">
                    <button
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={() => handleExport('csv')}
                      disabled={exporting}
                    >
                      Export as CSV
                    </button>
                    <button
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={() => handleExport('json')}
                      disabled={exporting}
                    >
                      Export as JSON
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white">Total VCards</h3>
          <p className="text-2xl font-bold text-purple-600">{stats.total}</p>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white">Active VCards</h3>
          <p className="text-2xl font-bold text-green-600">{stats.active}</p>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white">Inactive VCards</h3>
          <p className="text-2xl font-bold text-red-600">{stats.inactive}</p>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white">Unique Users</h3>
          <p className="text-2xl font-bold text-blue-600">{stats.users}</p>
        </div>
      </div>
      
      {hasActiveFilters() && (
        <ActiveFiltersVcards
          activeFilters={activeFilters} 
          resetFilters={resetFilters} 
        />
      )}

      <div className="overflow-x-auto bg-white dark:bg-gray-800 rounded-lg shadow">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                URL
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                User
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Created
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Views
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {currentPageVCards.length > 0 ? (
              currentPageVCards.map((vcard) => (
                <tr key={vcard.id} className="hover:bg-gray-50 dark:hover:bg-gray-750">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {vcard.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    <a 
                      href={`/vcard/${vcard.url}`} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-purple-600 hover:underline"
                    >
                      {vcard.url}
                    </a>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    <div className="flex items-center">
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">
                          {vcard.Users?.name || 'N/A'}
                        </div>
                        <div className="text-gray-500 dark:text-gray-400">
                          {vcard.Users?.email || 'N/A'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span 
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        vcard.is_active 
                          ? 'bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-200' 
                          : 'bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-200'
                      }`}
                    >
                      {vcard.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    {vcard.createdAt ? formatDate(vcard.createdAt) : 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    {vcard.views || 0}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <Link 
                        to={`/admin/vcards/${vcard.id}`}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        <FaEye className="inline mr-1" /> View
                      </Link>
                      <button
                        onClick={() => toggleVCardStatus(vcard.id, !vcard.is_active)}
                        className={`ml-3 ${
                          vcard.is_active 
                            ? 'text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300' 
                            : 'text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300'
                        }`}
                      >
                        {vcard.is_active ? 'Deactivate' : 'Activate'}
                      </button>
                      <button
                        onClick={() => handleDeleteVCard(vcard.id)}
                        className="ml-3 text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      >
                        <FaTrash className="inline mr-1" /> Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                  {hasActiveFilters() 
                    ? "No VCards match your filters" 
                    : "No VCards found"}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {filteredVCards && filteredVCards.length > 0 && totalPages > 1 && (
        <Pagination 
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={paginate}
        />
      )}
    </div>
  );
};

export default ListVCards;