import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { blockService, vcardService } from '../../services/api';
import LoadingSpinner from '../../Loading/LoadingSpinner';
import BlocksTable from '../../atoms/Tables/BlocksTable';
import { VCard } from '../../services/vcard';

const ListBlocks: React.FC = () => {
  const { vcardId } = useParams<{ vcardId: string }>();
  const [blocks, setBlocks] = useState<any[]>([]);
  const [vcard, setVcard] = useState<VCard>();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBlocks = async () => {
      try {
        setLoading(true);
        if (vcardId) {
          const response = await blockService.getByVcardId(vcardId);
          const res = await vcardService.getById(vcardId);
          setVcard(res.data);
          setBlocks(response.data || []);
        }
      } catch (error) {
        console.error('Failed to fetch blocks', error);
        toast.error('Failed to load blocks');
        setBlocks([]);
      } finally {
        setLoading(false);
      }
    };

    fetchBlocks();
  }, [vcardId]);




  const handleDeleteBlock = async (blockId: string) => {
    if (window.confirm('Are you sure you want to delete this block?')) {
      try {
        await blockService.delete(blockId);
        setBlocks(prev => prev.filter(block => block.id !== blockId));
        toast.success('Block deleted successfully');
      } catch (error) {
        console.error('Failed to delete block', error);
        toast.error('Failed to delete block');
      }
    }
  };

  const handleToggleStatus = async (blockId: string, isActive: boolean) => {
    try {
      setBlocks(prevBlocks =>
        prevBlocks.map(block =>
          block.id === blockId ? { ...block, status: isActive } : block
        )
      );

      await blockService.update(blockId, { status: isActive });
      toast.success(`Block ${isActive ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      console.error('Failed to toggle block status', error);
      toast.error('Failed to update block status');
      setBlocks(prevBlocks =>
        prevBlocks.map(block =>
          block.id === blockId ? { ...block, status: !isActive } : block
        )
      );
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="p-4 sm:p-6 lg:px-8 xl:px-28 w-full max-w-[90rem] mx-auto">
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
      />

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 sm:mb-8 gap-4">
        <div className="w-full md:w-auto">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">Blocks Management</h1>
          <p className="text-primary mt-1 sm:mt-2 text-sm sm:text-base">
            Manage blocks for VCard: {vcard.name}
          </p>
        </div>
      </div>

      <BlocksTable 
        blocks={blocks}
        onDelete={handleDeleteBlock}
        onToggleStatus={handleToggleStatus}
      />

    </div>
  );
};

export default ListBlocks;